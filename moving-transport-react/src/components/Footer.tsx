import { Box, Container, Grid, Typography, Link, IconButton, Divider } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import FacebookIcon from '@mui/icons-material/Facebook';
import TwitterIcon from '@mui/icons-material/Twitter';
import InstagramIcon from '@mui/icons-material/Instagram';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';

const Footer = () => {
  return (
    <Box sx={{ bgcolor: '#1a1a1a', color: 'white', py: 6 }}>
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <LocalShippingIcon sx={{ mr: 1, color: 'primary.main' }} />
              <Typography variant="h6" color="primary.main" fontWeight={700}>
                MovingTransport
              </Typography>
            </Box>
            <Typography variant="body2" color="#ccc" sx={{ mb: 2 }}>
              Connecting people with transport services for easier, smarter moving.
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton size="small" sx={{ bgcolor: '#333', color: 'white', '&:hover': { bgcolor: 'primary.main' } }}>
                <FacebookIcon />
              </IconButton>
              <IconButton size="small" sx={{ bgcolor: '#333', color: 'white', '&:hover': { bgcolor: 'primary.main' } }}>
                <TwitterIcon />
              </IconButton>
              <IconButton size="small" sx={{ bgcolor: '#333', color: 'white', '&:hover': { bgcolor: 'primary.main' } }}>
                <InstagramIcon />
              </IconButton>
              <IconButton size="small" sx={{ bgcolor: '#333', color: 'white', '&:hover': { bgcolor: 'primary.main' } }}>
                <LinkedInIcon />
              </IconButton>
            </Box>
          </Grid>
          
          <Grid item xs={12} sm={4} md={2}>
            <Typography variant="subtitle1" sx={{ mb: 2 }}>
              Company
            </Typography>
            <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
              <Box component="li" sx={{ mb: 1 }}>
                <Link component={RouterLink} to="/about" color="#ccc" underline="hover">
                  About Us
                </Link>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Link component={RouterLink} to="/careers" color="#ccc" underline="hover">
                  Careers
                </Link>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Link component={RouterLink} to="/press" color="#ccc" underline="hover">
                  Press
                </Link>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Link component={RouterLink} to="/blog" color="#ccc" underline="hover">
                  Blog
                </Link>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={12} sm={4} md={3}>
            <Typography variant="subtitle1" sx={{ mb: 2 }}>
              Support
            </Typography>
            <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
              <Box component="li" sx={{ mb: 1 }}>
                <Link component={RouterLink} to="/help" color="#ccc" underline="hover">
                  Help Center
                </Link>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Link component={RouterLink} to="/safety" color="#ccc" underline="hover">
                  Safety
                </Link>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Link component={RouterLink} to="/cancellation" color="#ccc" underline="hover">
                  Cancellation Options
                </Link>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Link component={RouterLink} to="/covid" color="#ccc" underline="hover">
                  COVID-19 Resources
                </Link>
              </Box>
            </Box>
          </Grid>
          
          <Grid item xs={12} sm={4} md={3}>
            <Typography variant="subtitle1" sx={{ mb: 2 }}>
              Legal
            </Typography>
            <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
              <Box component="li" sx={{ mb: 1 }}>
                <Link component={RouterLink} to="/terms" color="#ccc" underline="hover">
                  Terms of Service
                </Link>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Link component={RouterLink} to="/privacy" color="#ccc" underline="hover">
                  Privacy Policy
                </Link>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Link component={RouterLink} to="/cookies" color="#ccc" underline="hover">
                  Cookie Policy
                </Link>
              </Box>
              <Box component="li" sx={{ mb: 1 }}>
                <Link component={RouterLink} to="/accessibility" color="#ccc" underline="hover">
                  Accessibility
                </Link>
              </Box>
            </Box>
          </Grid>
        </Grid>
        
        <Divider sx={{ my: 4, borderColor: '#333' }} />
        
        <Typography variant="body2" color="#ccc" align="center">
          &copy; {new Date().getFullYear()} MovingTransport. All rights reserved.
        </Typography>
      </Container>
    </Box>
  );
};

export default Footer;
