import { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Slider,
  FormControl,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Radio,
  RadioGroup,
  Button,
  Divider,
} from '@mui/material';

import type { FilterOptions, VehicleTypes } from '../types/models';

interface SearchFiltersProps {
  onFilterChange: (filters: FilterOptions) => void;
}

const SearchFilters = ({ onFilterChange }: SearchFiltersProps) => {
  const [priceRange, setPriceRange] = useState<number[]>([0, 150]);
  const [distance, setDistance] = useState<number>(10);
  const [vehicleTypes, setVehicleTypes] = useState<VehicleTypes>({
    smallVan: false,
    largeVan: false,
    smallTruck: false,
    largeTruck: false,
    pickupTruck: false,
    boxTruck: false,
  });
  const [sortBy, setSortBy] = useState('recommended');

  const handlePriceChange = (_event: Event, newValue: number | number[]) => {
    setPriceRange(newValue as number[]);
  };

  const handleDistanceChange = (_event: Event, newValue: number | number[]) => {
    setDistance(newValue as number);
  };

  const handleVehicleTypeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setVehicleTypes({
      ...vehicleTypes,
      [event.target.name]: event.target.checked,
    });
  };

  const handleSortChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSortBy(event.target.value);
  };

  const handleApplyFilters = () => {
    onFilterChange({
      priceRange,
      distance,
      vehicleTypes,
      sortBy,
    });
  };

  const handleResetFilters = () => {
    setPriceRange([0, 150]);
    setDistance(10);
    setVehicleTypes({
      smallVan: false,
      largeVan: false,
      smallTruck: false,
      largeTruck: false,
      pickupTruck: false,
      boxTruck: false,
    });
    setSortBy('recommended');

    onFilterChange({
      priceRange: [0, 150],
      distance: 10,
      vehicleTypes: {
        smallVan: false,
        largeVan: false,
        smallTruck: false,
        largeTruck: false,
        pickupTruck: false,
        boxTruck: false,
      },
      sortBy: 'recommended',
    });
  };

  return (
    <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
      <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
        Filters
      </Typography>

      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle2" gutterBottom>
          Price Range (per hour)
        </Typography>
        <Slider
          value={priceRange}
          onChange={handlePriceChange}
          valueLabelDisplay="auto"
          min={0}
          max={150}
          step={5}
          marks={[
            { value: 0, label: '$0' },
            { value: 150, label: '$150' },
          ]}
        />
        <Typography variant="body2" color="text.secondary">
          ${priceRange[0]} - ${priceRange[1]}
        </Typography>
      </Box>

      <Divider sx={{ my: 3 }} />

      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle2" gutterBottom>
          Distance
        </Typography>
        <Slider
          value={distance}
          onChange={handleDistanceChange}
          valueLabelDisplay="auto"
          min={1}
          max={50}
          marks={[
            { value: 1, label: '1 mi' },
            { value: 50, label: '50 mi' },
          ]}
        />
        <Typography variant="body2" color="text.secondary">
          Within {distance} miles
        </Typography>
      </Box>

      <Divider sx={{ my: 3 }} />

      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle2" gutterBottom>
          Vehicle Type
        </Typography>
        <FormGroup>
          <FormControlLabel
            control={
              <Checkbox
                checked={vehicleTypes.smallVan}
                onChange={handleVehicleTypeChange}
                name="smallVan"
              />
            }
            label="Small Van"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={vehicleTypes.largeVan}
                onChange={handleVehicleTypeChange}
                name="largeVan"
              />
            }
            label="Large Van"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={vehicleTypes.smallTruck}
                onChange={handleVehicleTypeChange}
                name="smallTruck"
              />
            }
            label="Small Truck"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={vehicleTypes.largeTruck}
                onChange={handleVehicleTypeChange}
                name="largeTruck"
              />
            }
            label="Large Truck"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={vehicleTypes.pickupTruck}
                onChange={handleVehicleTypeChange}
                name="pickupTruck"
              />
            }
            label="Pickup Truck"
          />
          <FormControlLabel
            control={
              <Checkbox
                checked={vehicleTypes.boxTruck}
                onChange={handleVehicleTypeChange}
                name="boxTruck"
              />
            }
            label="Box Truck"
          />
        </FormGroup>
      </Box>

      <Divider sx={{ my: 3 }} />

      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle2" gutterBottom>
          Sort By
        </Typography>
        <FormControl component="fieldset">
          <RadioGroup value={sortBy} onChange={handleSortChange}>
            <FormControlLabel value="recommended" control={<Radio />} label="Recommended" />
            <FormControlLabel value="price_low" control={<Radio />} label="Price: Low to High" />
            <FormControlLabel value="price_high" control={<Radio />} label="Price: High to Low" />
            <FormControlLabel value="rating" control={<Radio />} label="Highest Rating" />
            <FormControlLabel value="distance" control={<Radio />} label="Closest First" />
          </RadioGroup>
        </FormControl>
      </Box>

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Button variant="contained" color="primary" onClick={handleApplyFilters}>
          Apply Filters
        </Button>
        <Button variant="outlined" onClick={handleResetFilters}>
          Reset Filters
        </Button>
      </Box>
    </Paper>
  );
};

export default SearchFilters;
