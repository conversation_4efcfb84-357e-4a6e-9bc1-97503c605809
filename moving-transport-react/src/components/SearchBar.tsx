import { useState } from 'react';
import {
  Paper,
  InputBase,
  IconButton,
  Button,
  Divider,
  Chip,
  useTheme,
  alpha,
  Tooltip,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import TuneIcon from '@mui/icons-material/Tune';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import FilterModal from './FilterModal';
import type { FilterOptions } from '../types/models';

interface SearchBarProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onSearch: (e: React.FormEvent) => void;
  filters: FilterOptions;
  onFilterChange: (filters: FilterOptions) => void;
}

const SearchBar = ({ searchQuery, onSearchChange, onSearch, filters, onFilterChange }: SearchBarProps) => {
  const theme = useTheme();
  const [filterModalOpen, setFilterModalOpen] = useState(false);

  // Count active filters
  const countActiveFilters = () => {
    let count = 0;

    // Check price range
    if (filters.priceRange[0] > 0 || filters.priceRange[1] < 150) {
      count++;
    }

    // Check distance
    if (filters.distance !== 10) {
      count++;
    }

    // Check vehicle types
    const activeVehicleTypes = Object.values(filters.vehicleTypes).filter(Boolean).length;
    if (activeVehicleTypes > 0) {
      count += activeVehicleTypes;
    }

    // Check sort
    if (filters.sortBy !== 'recommended') {
      count++;
    }

    return count;
  };

  return (
    <>
      <Paper
        component="form"
        onSubmit={onSearch}
        elevation={0}
        sx={{
          p: '2px 4px',
          display: 'flex',
          alignItems: 'center',
          width: '100%',
          borderRadius: 3,
          border: `1px solid ${alpha(theme.palette.divider, 0.15)}`,
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
          transition: 'all 0.3s ease',
          '&:hover': {
            boxShadow: '0 6px 24px rgba(0, 0, 0, 0.08)',
            borderColor: alpha(theme.palette.primary.main, 0.3),
          },
          '&:focus-within': {
            boxShadow: `0 0 0 3px ${alpha(theme.palette.primary.main, 0.2)}`,
            borderColor: theme.palette.primary.main,
          },
        }}
      >
        <IconButton sx={{ p: '10px', color: theme.palette.primary.main }} aria-label="location">
          <LocationOnIcon />
        </IconButton>
        <InputBase
          sx={{ ml: 1, flex: 1 }}
          placeholder="Where do you need transport?"
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          inputProps={{ 'aria-label': 'search for transport' }}
        />
        <Divider sx={{ height: 28, m: 0.5 }} orientation="vertical" />
        <Tooltip title="Apply filters">
          <Button
            onClick={() => setFilterModalOpen(true)}
            sx={{
              mx: 1,
              px: 2,
              py: 1,
              borderRadius: 2,
              textTransform: 'none',
              color: countActiveFilters() > 0 ? theme.palette.primary.main : theme.palette.text.secondary,
              fontWeight: 500,
              backgroundColor: countActiveFilters() > 0 ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
              '&:hover': {
                backgroundColor: alpha(theme.palette.primary.main, 0.15),
              },
            }}
            startIcon={<TuneIcon />}
            endIcon={countActiveFilters() > 0 ?
              <Chip
                label={countActiveFilters()}
                size="small"
                sx={{
                  height: 20,
                  minWidth: 20,
                  backgroundColor: theme.palette.primary.main,
                  color: 'white',
                  fontWeight: 'bold'
                }}
              /> : null
            }
          >
            Filters
          </Button>
        </Tooltip>
        <Divider sx={{ height: 28, m: 0.5 }} orientation="vertical" />
        <IconButton
          type="submit"
          sx={{
            p: '10px',
            color: 'white',
            backgroundColor: theme.palette.primary.main,
            borderRadius: 2,
            mr: 0.5,
            '&:hover': {
              backgroundColor: theme.palette.primary.dark,
            }
          }}
          aria-label="search"
        >
          <SearchIcon />
        </IconButton>
      </Paper>

      <FilterModal
        open={filterModalOpen}
        onClose={() => setFilterModalOpen(false)}
        filters={filters}
        onApplyFilters={onFilterChange}
      />
    </>
  );
};

export default SearchBar;
