import { Box, Container, Typography, Card, CardContent, Avatar, Rating, useTheme, useMediaQuery, Grid } from '@mui/material';
import { testimonials } from '../data/mockData';

const TestimonialsSection = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  // Determine how many testimonials to show per row based on screen size
  const testimonialsPerRow = isMobile ? 1 : isTablet ? 2 : 3;

  return (
    <Box
      id="testimonials"
      sx={{
        py: 8,
        bgcolor: 'white',
      }}
    >
      <Container maxWidth="lg">
        <Typography
          variant="h3"
          component="h2"
          align="center"
          sx={{ mb: 6, fontWeight: 600 }}
        >
          What Our Customers Say
        </Typography>

        <Grid container spacing={3}>
          {testimonials.map((testimonial) => (
            <Grid item xs={12} sm={testimonialsPerRow === 1 ? 12 : 6} md={testimonialsPerRow === 3 ? 4 : 6} key={testimonial.id}>
              <Card
                elevation={2}
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  borderRadius: 2,
                  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-10px)',
                    boxShadow: 8,
                  },
                }}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      src={testimonial.avatar}
                      alt={testimonial.name}
                      sx={{ width: 60, height: 60, mr: 2 }}
                    />
                    <Box>
                      <Typography variant="h6" component="h3" sx={{ fontWeight: 600 }}>
                        {testimonial.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {testimonial.location}
                      </Typography>
                    </Box>
                  </Box>
                  <Rating value={testimonial.rating} readOnly precision={0.5} sx={{ mb: 2 }} />
                  <Typography variant="body1" sx={{ fontStyle: 'italic' }}>
                    "{testimonial.text}"
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  );
};

export default TestimonialsSection;
