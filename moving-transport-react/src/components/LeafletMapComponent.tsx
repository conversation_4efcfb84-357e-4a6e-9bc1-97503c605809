import { useRef, useEffect, useState } from 'react';
import { Box } from '@mui/material';
import type { TransportOption } from '../types/models';

// This component will be implemented with Leaflet
// You'll need to install: npm install leaflet react-leaflet @types/leaflet
// or if using pnpm: pnpm add leaflet react-leaflet @types/leaflet

interface LeafletMapComponentProps {
  transportOptions: TransportOption[];
  selectedTransport: TransportOption | null;
  onMarkerClick: (transport: TransportOption) => void;
}

const LeafletMapComponent = ({ transportOptions, selectedTransport, onMarkerClick }: LeafletMapComponentProps) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [mapInitialized, setMapInitialized] = useState(false);
  
  // This will store references to the map and markers
  const mapInstanceRef = useRef<any>(null);
  const markersRef = useRef<{[key: number]: any}>({});
  
  // Initialize map when component mounts
  useEffect(() => {
    if (!mapRef.current || typeof window === 'undefined') return;
    
    // Import Leaflet dynamically to avoid SSR issues
    import('leaflet').then((L) => {
      // If map is already initialized, return
      if (mapInstanceRef.current) return;
      
      // Initialize map
      const map = L.map(mapRef.current!).setView([40.7128, -74.006], 11);
      
      // Add OpenStreetMap tiles
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
      }).addTo(map);
      
      // Store map instance
      mapInstanceRef.current = map;
      setMapInitialized(true);
      
      // Clean up on unmount
      return () => {
        if (mapInstanceRef.current) {
          mapInstanceRef.current.remove();
          mapInstanceRef.current = null;
        }
      };
    });
  }, []);
  
  // Add markers when transportOptions change or map is initialized
  useEffect(() => {
    if (!mapInitialized || !mapInstanceRef.current || transportOptions.length === 0) return;
    
    import('leaflet').then((L) => {
      const map = mapInstanceRef.current;
      
      // Clear existing markers
      Object.values(markersRef.current).forEach((marker: any) => {
        marker.remove();
      });
      markersRef.current = {};
      
      // Create bounds to fit all markers
      const bounds = L.latLngBounds([]);
      
      // Add markers for each transport option
      transportOptions.forEach(transport => {
        const { lat, lng } = transport.coordinates;
        
        // Create custom icon element
        const customIcon = L.divIcon({
          className: 'custom-marker',
          html: `<div style="
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: ${selectedTransport?.id === transport.id ? 'rgba(25, 118, 210, 0.95)' : 'rgba(99, 102, 241, 0.7)'};
            color: white;
            font-weight: bold;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            border: ${selectedTransport?.id === transport.id ? '3px solid white' : '2px solid rgba(255, 255, 255, 0.8)'};
            ${selectedTransport?.id === transport.id ? 'animation: pulse 1.5s infinite;' : ''}
          ">$${transport.pricePerHour}</div>`,
          iconSize: [40, 40],
          iconAnchor: [20, 20]
        });
        
        // Create marker
        const marker = L.marker([lat, lng], { icon: customIcon })
          .addTo(map)
          .on('click', () => {
            onMarkerClick(transport);
          });
        
        // Add popup
        marker.bindPopup(`
          <div style="padding: 10px;">
            <div style="font-weight: bold; margin-bottom: 5px;">${transport.title}</div>
            <div>${transport.type} • ${transport.features.capacity}</div>
            <div style="color: #6366F1; font-weight: bold; margin-top: 5px;">${transport.price}</div>
          </div>
        `);
        
        // Store marker reference
        markersRef.current[transport.id] = marker;
        
        // Extend bounds
        bounds.extend([lat, lng]);
      });
      
      // Fit map to bounds if there are markers
      if (bounds.isValid()) {
        map.fitBounds(bounds, {
          padding: [70, 70],
          maxZoom: 13
        });
      }
    });
  }, [transportOptions, mapInitialized, selectedTransport, onMarkerClick]);
  
  // Center map on selected transport
  useEffect(() => {
    if (!mapInitialized || !mapInstanceRef.current || !selectedTransport) return;
    
    const marker = markersRef.current[selectedTransport.id];
    if (marker) {
      import('leaflet').then(() => {
        // Fly to marker
        mapInstanceRef.current.setView(marker.getLatLng(), 13, {
          animate: true,
          duration: 1
        });
        
        // Open popup
        marker.openPopup();
      });
    } else if (selectedTransport.coordinates) {
      // If marker not found but coordinates exist
      const { lat, lng } = selectedTransport.coordinates;
      mapInstanceRef.current.setView([lat, lng], 13, {
        animate: true,
        duration: 1
      });
    }
  }, [selectedTransport, mapInitialized]);
  
  return (
    <Box
      ref={mapRef}
      className="leaflet-map-container"
      sx={{
        height: '100%',
        width: '100%',
        minHeight: '500px',
        borderRadius: 2,
        overflow: 'hidden',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
      }}
    />
  );
};

export default LeafletMapComponent;
